import { colors } from '@/constants/theme';
import React from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');
const FRAME_WIDTH = width * 0.8;
const FRAME_HEIGHT = FRAME_WIDTH * 1.3; // Portrait aspect ratio

interface CameraFrameProps {
  type: 'front' | 'right' | 'left';
}

export const CameraFrame: React.FC<CameraFrameProps> = ({ type }) => {
  return (
    <View style={styles.container}>
      <View style={styles.overlay}>
        <View style={styles.transparent}>
          <View style={styles.frame}>
            <View style={styles.corner} />
            <View style={[styles.corner, styles.topRight]} />
            <View style={[styles.corner, styles.bottomRight]} />
            <View style={[styles.corner, styles.bottomLeft]} />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
    pointerEvents: 'none',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  transparent: {
    width: FRAME_WIDTH,
    height: FRAME_HEIGHT,
    backgroundColor: 'transparent',
  },
  frame: {
    flex: 1,
    borderWidth: 2,
    borderColor: colors.text.light,
    borderRadius: 12,
  },
  corner: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderColor: colors.accent,
    borderTopWidth: 4,
    borderLeftWidth: 4,
    top: -2,
    left: -2,
    borderTopLeftRadius: 8,
  },
  topRight: {
    right: -2,
    left: undefined,
    borderTopRightRadius: 8,
    borderLeftWidth: 0,
    borderRightWidth: 4,
  },
  bottomRight: {
    right: -2,
    bottom: -2,
    top: undefined,
    left: undefined,
    borderBottomRightRadius: 8,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderRightWidth: 4,
    borderBottomWidth: 4,
  },
  bottomLeft: {
    bottom: -2,
    top: undefined,
    borderBottomLeftRadius: 8,
    borderTopWidth: 0,
    borderBottomWidth: 4,
  },
});
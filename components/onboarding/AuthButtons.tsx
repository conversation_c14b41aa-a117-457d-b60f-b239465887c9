import { colors, spacing } from '@/constants/theme';
import React from 'react';
import { StyleSheet, View, Platform } from 'react-native';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';
import { router } from 'expo-router';

export const AuthButtons: React.FC = () => {
  const { signInWithProvider, loading } = useAuth();

  const handleGoogleSignIn = async () => {
    const success = await signInWithProvider('google');
    if (success) {
      router.replace('/onboarding/intro');
    }
  };

  const handleAppleSignIn = async () => {
    const success = await signInWithProvider('apple');
    if (success) {
      router.replace('/onboarding/intro');
    }
  };

  const handleEmailSignIn = () => {
    router.push('/onboarding/auth');
  };

  return (
    <View style={styles.container}>
      <Button
        title="Continue with Google"
        onPress={handleGoogleSignIn}
        variant="outline"
        loading={loading}
        style={styles.button}
      />
      
      {Platform.OS === 'ios' && (
        <Button
          title="Continue with Apple"
          onPress={handleAppleSignIn}
          variant="outline"
          loading={loading}
          style={styles.button}
        />
      )}
      
      <Button
        title="Continue with Email"
        onPress={handleEmailSignIn}
        variant="primary"
        style={styles.button}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  button: {
    marginBottom: spacing.m,
    width: '100%',
  },
});
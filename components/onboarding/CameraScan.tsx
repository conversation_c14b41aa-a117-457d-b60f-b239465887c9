import React from "react";
import { StyleSheet, View, TouchableOpacity, Modal, Platform, ActivityIndicator } from "react-native";
import { H3, Body } from "@/components/ui/Typography";
import { colors, spacing, borderRadius } from "@/constants/theme";
import { Button } from "@/components/ui/Button";
import { CameraView } from "expo-camera";
import { CameraFrame } from "@/components/onboarding/CameraFrame";
import { FaceGuide } from "@/components/onboarding/FaceGuide";
import { Image } from "expo-image";
import { Image as ImageIcon, Check, X, RotateCcw } from "lucide-react-native";
import { useCamera } from "@/hooks/useCamera";

interface CameraScanProps {
  scanType: 'front' | 'right' | 'left';
  onPhotoTaken: (imageUri: string) => void;
  instruction: string;
}

export function CameraScan({ scanType, onPhotoTaken, instruction }: CameraScanProps) {
  const {
    permission,
    requestPermission,
    facing,
    capturedImage,
    showModal,
    closeModal,
    cameraRef,
    isReady,
    handleTakePhoto,
    handlePickImage,
    flipCamera,
    onCameraReady,
  } = useCamera(`${scanType}Scan`);

  const handleUsePhoto = () => {
    console.log(`📸 [${scanType}Scan] Using photo:`, capturedImage);
    if (capturedImage) {
      onPhotoTaken(capturedImage);
      closeModal();
    }
  };

  const handleRetake = () => {
    console.log(`📸 [${scanType}Scan] Retaking photo`);
    closeModal();
  };

  if (!permission) {
    return (
      <View style={styles.permissionContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Body style={styles.permissionText}>
          Initializing camera...
        </Body>
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={styles.permissionContainer}>
        <H3 style={styles.permissionTitle}>Camera Access Required</H3>
        <Body style={styles.permissionText}>
          Please allow camera access to continue with your skin scan
        </Body>
        <Button title="Allow Camera Access" onPress={requestPermission} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          facing={facing}
          ref={cameraRef}
          onCameraReady={onCameraReady}
        />
        <CameraFrame type={scanType} />
        <View style={styles.instructionContainer}>
          <View style={styles.faceGuideContainer}>
            <FaceGuide type={scanType} size={50} />
          </View>
          <View style={styles.instructionBox}>
            <Body style={styles.instructionText}>
              {instruction}
            </Body>
          </View>
        </View>
      </View>
      
      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.galleryButton}
          onPress={handlePickImage}
        >
          <ImageIcon size={24} color={colors.text.light} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.captureButton, (!isReady && !permission?.granted) && styles.captureButtonDisabled]}
          onPress={handleTakePhoto}
          disabled={!permission?.granted}
        >
          <View style={styles.captureButtonInner} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.flipButton}
          onPress={flipCamera}
        >
          <RotateCcw size={24} color={colors.text.light} />
        </TouchableOpacity>
      </View>

      <Modal visible={showModal} transparent animationType="fade">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <H3 style={styles.modalTitle}>Review Photo</H3>
            <Image
              source={{ uri: capturedImage || "" }}
              style={[styles.previewImage, facing === 'front' && styles.mirroredImage]}
              contentFit="cover"
            />
            <View style={styles.modalButtons}>
              <Button
                title="Retake"
                onPress={handleRetake}
                variant="outline"
                icon={<X size={20} color={colors.primary} />}
                style={styles.modalButton}
              />
              <Button
                title="Use Photo"
                onPress={handleUsePhoto}
                icon={<Check size={20} color={colors.text.light} />}
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  instructionContainer: {
    position: "absolute",
    top: 40,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  faceGuideContainer: {
    marginBottom: 16,
  },
  instructionBox: {
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    paddingVertical: spacing.s,
    paddingHorizontal: spacing.m,
    borderRadius: borderRadius.round,
  },
  instructionText: {
    color: colors.text.light,
    textAlign: "center",
  },
  controls: {
    position: "absolute",
    bottom: Platform.OS === 'ios' ? 60 : 40,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    alignItems: "center",
    justifyContent: "center",
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: colors.text.light,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  captureButtonInner: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: colors.text.light,
  },
  captureButtonDisabled: {
    opacity: 0.5,
  },

  flipButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    alignItems: "center",
    justifyContent: "center",
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.l,
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.l,
    padding: spacing.l,
    width: "100%",
    maxWidth: 400,
  },
  modalTitle: {
    marginBottom: spacing.m,
    textAlign: "center",
  },
  previewImage: {
    width: "100%",
    height: 400,
    borderRadius: borderRadius.m,
    marginBottom: spacing.l,
  },
  mirroredImage: {
    transform: [{ scaleX: -1 }],
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  modalButton: {
    flex: 0.48,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  },
  permissionTitle: {
    marginBottom: spacing.m,
  },
  permissionText: {
    textAlign: "center",
    marginBottom: spacing.xl,
    color: colors.text.secondary,
  },
});
{"expo": {"name": "AI Skincare Companion", "slug": "ai-skincare-companion-1ahyfo8", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.ai-skincare-companion-1ahyfo8"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.ai-skincare-companion-1ahyfo8"}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.com/"}]], "experiments": {"typedRoutes": true}}}
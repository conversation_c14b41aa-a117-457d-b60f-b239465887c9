{"compilerOptions": {"target": "esnext", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "jsx": "preserve", "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"], "extends": "expo/tsconfig.base"}
{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "bunx rork start -p ugrajjno9ln5y5s19weir --tunnel", "start-web": "bunx rork start -p ugrajjno9ln5y5s19weir --web --tunnel", "start-web-dev": "DEBUG=expo* bunx rork start -p ugrajjno9ln5y5s19weir --web --tunnel"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@nkzw/create-context-hook": "^1.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "7.1.6", "@tanstack/react-query": "^5.83.0", "expo": "^53.0.4", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@playwright/test": "^1.54.1", "@types/react": "~19.0.10", "playwright": "^1.54.1", "typescript": "~5.8.3"}, "private": true}
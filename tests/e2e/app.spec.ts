import { test, expect } from '@playwright/test';

test.describe('AI Skincare Companion App', () => {
  test('should load the splash screen', async ({ page }) => {
    await page.goto('/');
    
    // Wait for the app to load
    await page.waitForTimeout(2000);
    
    // Check if we're redirected to splash
    await expect(page).toHaveURL(/.*splash/);
  });

  test('should navigate through onboarding flow', async ({ page }) => {
    await page.goto('/onboarding/welcome');
    
    // Check welcome screen
    await expect(page.locator('text=Welcome')).toBeVisible();
    
    // Navigate to auth
    await page.click('text=Get Started');
    await expect(page).toHaveURL(/.*auth/);
  });

  test('should handle chat interface', async ({ page }) => {
    // Skip onboarding for this test
    await page.goto('/(tabs)/chat');
    
    // Check if chat interface loads
    await expect(page.locator('[data-testid="chat-container"]')).toBeVisible();
  });

  test('should handle camera/scan functionality', async ({ page }) => {
    await page.goto('/scan');
    
    // Check if scan interface loads
    await expect(page.locator('text=Skin Scan')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Check mobile layout
    await expect(page.locator('body')).toBeVisible();
  });
});

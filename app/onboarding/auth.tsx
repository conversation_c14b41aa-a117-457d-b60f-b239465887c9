import React, { useState } from "react";
import { StyleSheet, View, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView } from "react-native";
import { H2, Body, BodySmall } from "@/components/ui/Typography";
import { colors, spacing, borderRadius } from "@/constants/theme";
import { Button } from "@/components/ui/Button";
import { useAuth } from "@/hooks/useAuth";
import { router } from "expo-router";
import Svg, { Path, Circle, Defs, LinearGradient, Stop, Ellipse } from 'react-native-svg';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';

const SkincareLogo = ({ size = 60 }: { size?: number }) => (
  <Svg width={size} height={size} viewBox="0 0 100 100">
    <Defs>
      <LinearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <Stop offset="0%" stopColor={colors.primary} />
        <Stop offset="100%" stopColor={colors.accent} />
      </LinearGradient>
    </Defs>
    <Circle
      cx="50"
      cy="50"
      r="40"
      fill="rgba(255, 255, 255, 0.05)"
      stroke="url(#logoGradient)"
      strokeWidth="2.5"
    />
    <Circle
      cx="50"
      cy="50"
      r="28"
      fill="none"
      stroke="url(#logoGradient)"
      strokeWidth="1"
      opacity="0.4"
    />
    <Ellipse cx="42" cy="42" rx="2" ry="3" fill={colors.primary} opacity="0.7" />
    <Ellipse cx="58" cy="42" rx="2" ry="3" fill={colors.primary} opacity="0.7" />
    <Path
      d="M50 48 L49 52 Q50 54 51 52 L50 48"
      fill="none"
      stroke={colors.primary}
      strokeWidth="1.5"
      opacity="0.6"
    />
    <Path
      d="M45 58 Q50 62 55 58"
      fill="none"
      stroke={colors.accent}
      strokeWidth="2"
      opacity="0.8"
    />
  </Svg>
);

export default function AuthScreen() {
  const [isSignUp, setIsSignUp] = useState(false);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const { signIn, signUp, signInWithProvider, loading } = useAuth();

  const handleSubmit = async () => {
    if (isSignUp) {
      const success = await signUp(name, email, password);
      if (success) {
        router.replace("/onboarding/intro");
      }
    } else {
      const success = await signIn(email, password);
      if (success) {
        router.replace("/onboarding/intro");
      }
    }
  };

  const handleProviderAuth = async (provider: 'google' | 'apple') => {
    const success = await signInWithProvider(provider);
    if (success) {
      router.replace("/onboarding/intro");
    }
  };

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 60 : 0}
    >
      <ExpoLinearGradient
        colors={[colors.background, `${colors.primary}08`, colors.background]}
        style={styles.gradient}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            <View style={styles.header}>
              <SkincareLogo size={70} />
              <H2 style={styles.title}>SkinAI</H2>
              <Body style={styles.subtitle}>
                {isSignUp
                  ? "Join thousands transforming their skin"
                  : "Welcome back to your skincare journey"}
              </Body>
            </View>

            <View style={styles.authSection}>
              <View style={styles.providerButtons}>
                <Button
                  title="Continue with Google"
                  onPress={() => handleProviderAuth('google')}
                  variant="outline"
                  style={styles.providerButton}
                  loading={loading}
                />
                {Platform.OS === 'ios' && (
                  <Button
                    title="Continue with Apple"
                    onPress={() => handleProviderAuth('apple')}
                    variant="outline"
                    style={styles.providerButton}
                    loading={loading}
                  />
                )}
              </View>
              
              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Body style={styles.dividerText}>or continue with email</Body>
                <View style={styles.dividerLine} />
              </View>

              <View style={styles.form}>
                {isSignUp && (
                  <View style={styles.inputContainer}>
                    <TextInput
                      style={styles.input}
                      placeholder="Full Name"
                      value={name}
                      onChangeText={setName}
                      autoCapitalize="words"
                      placeholderTextColor={colors.text.secondary}
                    />
                  </View>
                )}

                <View style={styles.inputContainer}>
                  <TextInput
                    style={styles.input}
                    placeholder="Email Address"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    placeholderTextColor={colors.text.secondary}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <TextInput
                    style={styles.input}
                    placeholder="Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                    placeholderTextColor={colors.text.secondary}
                  />
                </View>

                <Button
                  title={isSignUp ? "Create Account" : "Sign In"}
                  onPress={handleSubmit}
                  loading={loading}
                  style={styles.submitButton}
                  disabled={
                    (isSignUp && (!name || !email || !password)) ||
                    (!isSignUp && (!email || !password))
                  }
                />
              </View>
            </View>

            <View style={styles.footer}>
              <Body style={styles.footerText}>
                {isSignUp ? "Already have an account?" : "Don't have an account?"}
              </Body>
              <TouchableOpacity onPress={toggleMode} style={styles.footerButton}>
                <Body style={styles.footerLink}>
                  {isSignUp ? "Sign In" : "Sign Up"}
                </Body>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </ExpoLinearGradient>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    minHeight: '100%',
  },
  content: {
    flex: 1,
    padding: spacing.xl,
    justifyContent: "center",
  },
  header: {
    alignItems: "center",
    marginBottom: spacing.xl,
  },
  title: {
    marginTop: spacing.m,
    marginBottom: spacing.s,
    textAlign: "center",
    fontSize: 28,
    fontWeight: '700' as const,
    color: colors.text.primary,
  },
  subtitle: {
    color: colors.text.secondary,
    textAlign: "center",
    fontSize: 16,
    lineHeight: 22,
  },
  authSection: {
    marginBottom: spacing.xl,
  },
  providerButtons: {
    gap: spacing.m,
    marginBottom: spacing.l,
  },
  providerButton: {
    borderColor: colors.border,
    backgroundColor: colors.card,
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: spacing.l,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.border,
    opacity: 0.5,
  },
  dividerText: {
    marginHorizontal: spacing.m,
    color: colors.text.secondary,
    fontSize: 13,
  },
  form: {
    gap: spacing.m,
  },
  inputContainer: {
    // No margin needed due to gap in parent
  },
  input: {
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.l,
    padding: spacing.l,
    fontSize: 16,
    fontFamily: Platform.OS === "ios" ? "Avenir Next" : "sans-serif",
    color: colors.text.primary,
  },
  submitButton: {
    marginTop: spacing.m,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingBottom: Platform.OS === 'ios' ? spacing.l : spacing.m,
  },
  footerButton: {
    marginLeft: spacing.xs,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.s,
  },
  footerText: {
    color: colors.text.secondary,
    fontSize: 15,
  },
  footerLink: {
    color: colors.primary,
    fontWeight: "600" as const,
    fontSize: 15,
  },
});
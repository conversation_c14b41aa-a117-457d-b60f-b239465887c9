import React from "react";
import { router, Stack } from "expo-router";
import { useSkincare } from "@/hooks/useSkincare";
import { CameraScan } from "@/components/onboarding/CameraScan";

export default function LeftScanScreen() {
  const { saveScanResult } = useSkincare();

  const handlePhotoTaken = (imageUri: string) => {
    console.log('📸 [LeftScan] Photo taken:', imageUri);
    if (!imageUri) {
      console.error('📸 [LeftScan] No image URI provided');
      return;
    }
    saveScanResult("left", imageUri);
    console.log('📸 [LeftScan] Saved left scan result, navigating to analysis');
    router.push("/onboarding/analysis");
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <CameraScan
        scanType="left"
        onPhotoTaken={handlePhotoTaken}
        instruction="Turn your head to show the left side"
      />
    </>
  );
}


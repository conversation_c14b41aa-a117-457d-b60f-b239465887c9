import React from "react";
import { router, Stack } from "expo-router";
import { useSkincare } from "@/hooks/useSkincare";
import { CameraScan } from "@/components/onboarding/CameraScan";

export default function FrontScanScreen() {
  const { saveScanResult } = useSkincare();

  const handlePhotoTaken = (imageUri: string) => {
    console.log('📸 [FrontScan] Photo taken:', imageUri);
    if (!imageUri) {
      console.error('📸 [FrontScan] No image URI provided');
      return;
    }
    saveScanResult("front", imageUri);
    console.log('📸 [FrontScan] Saved front scan result, navigating to right scan');
    router.push("/onboarding/scan/right");
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <CameraScan
        scanType="front"
        onPhotoTaken={handlePhotoTaken}
        instruction="Center your face in the frame"
      />
    </>
  );
}


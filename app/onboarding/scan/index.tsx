import React from "react";
import { StyleSheet, View, Image, SafeAreaView } from "react-native";
import { H2, H3, Body } from "@/components/ui/Typography";
import { colors, spacing, borderRadius } from "@/constants/theme";
import { Button } from "@/components/ui/Button";
import { router } from "expo-router";
import { Camera } from "lucide-react-native";

export default function ScanIntroScreen() {
  const handleStart = () => {
    router.replace("/onboarding/scan/front");
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <H2 style={styles.title}>Let's Scan Your Face</H2>
        <Body style={styles.subtitle}>
          We'll need three photos of your face to analyze your skin condition
        </Body>

        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: "https://images.unsplash.com/photo-1608248597279-f99d160bfcbc?auto=format&fit=crop&q=80&w=387&ixlib=rb-4.0.3",
            }}
            style={styles.image}
            resizeMode="cover"
          />
        </View>

        <View style={styles.instructionsContainer}>
          <H3 style={styles.instructionsTitle}>Instructions:</H3>
          <View style={styles.instruction}>
            <View style={styles.bulletPoint} />
            <Body style={styles.instructionText}>
              Find a well-lit area, preferably with natural light
            </Body>
          </View>
          <View style={styles.instruction}>
            <View style={styles.bulletPoint} />
            <Body style={styles.instructionText}>
              Remove makeup and glasses before scanning
            </Body>
          </View>
          <View style={styles.instruction}>
            <View style={styles.bulletPoint} />
            <Body style={styles.instructionText}>
              Keep your face centered in the frame
            </Body>
          </View>
          <View style={styles.instruction}>
            <View style={styles.bulletPoint} />
            <Body style={styles.instructionText}>
              We'll need front, right, and left angles
            </Body>
          </View>
        </View>

        <Button
          title="Start Scan"
          onPress={handleStart}
          size="large"
          style={styles.button}
          icon={<Camera size={20} color={colors.text.light} />}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.xl,
  },
  title: {
    marginBottom: spacing.s,
  },
  subtitle: {
    color: colors.text.secondary,
    marginBottom: spacing.xl,
  },
  imageContainer: {
    height: 200,
    borderRadius: borderRadius.l,
    overflow: "hidden",
    marginBottom: spacing.xl,
  },
  image: {
    width: "100%",
    height: "100%",
  },
  instructionsContainer: {
    marginBottom: spacing.xl,
  },
  instructionsTitle: {
    marginBottom: spacing.m,
  },
  instruction: {
    flexDirection: "row",
    marginBottom: spacing.m,
    alignItems: "flex-start",
  },
  bulletPoint: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
    marginTop: 8,
    marginRight: spacing.m,
  },
  instructionText: {
    flex: 1,
  },
  button: {
    width: "100%",
  },
});
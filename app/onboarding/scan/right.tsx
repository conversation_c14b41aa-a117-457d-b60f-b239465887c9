import React from "react";
import { router, Stack } from "expo-router";
import { useSkincare } from "@/hooks/useSkincare";
import { CameraScan } from "@/components/onboarding/CameraScan";

export default function RightScanScreen() {
  const { saveScanResult } = useSkincare();

  const handlePhotoTaken = (imageUri: string) => {
    console.log('📸 [RightScan] Photo taken:', imageUri);
    if (!imageUri) {
      console.error('📸 [RightScan] No image URI provided');
      return;
    }
    saveScanResult("right", imageUri);
    console.log('📸 [RightScan] Saved right scan result, navigating to left scan');
    router.push("/onboarding/scan/left");
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <CameraScan
        scanType="right"
        onPhotoTaken={handlePhotoTaken}
        instruction="Turn your head to show the right side"
      />
    </>
  );
}


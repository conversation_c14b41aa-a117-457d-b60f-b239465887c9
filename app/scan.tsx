import React, { useState, useRef } from "react";
import { StyleSheet, View, TouchableOpacity, Modal, Platform, Alert, SafeAreaView } from "react-native";
import { H2, H3, Body } from "@/components/ui/Typography";
import { colors, spacing, borderRadius } from "@/constants/theme";
import { Button } from "@/components/ui/Button";
import { CameraView, CameraType, useCameraPermissions } from "expo-camera";
import { CameraFrame } from "@/components/onboarding/CameraFrame";
import { Image } from "expo-image";
import { router, Stack } from "expo-router";
import { Camera, Image as ImageIcon, Check, X, ArrowLeft } from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import { useSkincare } from "@/hooks/useSkincare";

export default function ScanScreen() {
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<CameraType>("front");
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [scanType, setScanType] = useState<'front' | 'right' | 'left'>('front');
  const cameraRef = useRef<any>(null);
  const { saveScanResult, analyzeSkin } = useSkincare();

  const handleTakePhoto = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePictureAsync();
        setCapturedImage(photo.uri);
        setShowModal(true);
      } catch (error) {
        console.error("Error taking picture:", error);
        Alert.alert("Error", "Failed to take picture. Please try again.");
      }
    }
  };

  const handlePickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [3, 4],
      quality: 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      setCapturedImage(result.assets[0].uri);
      setShowModal(true);
    }
  };

  const handleUsePhoto = async () => {
    if (capturedImage) {
      saveScanResult(scanType, capturedImage);
      setShowModal(false);
      
      if (scanType === 'front') {
        setScanType('right');
      } else if (scanType === 'right') {
        setScanType('left');
      } else {
        // All photos taken, analyze skin
        const result = await analyzeSkin();
        if (result) {
          router.back();
        }
      }
    }
  };

  const handleRetake = () => {
    setCapturedImage(null);
    setShowModal(false);
  };

  const handleBack = () => {
    router.back();
  };

  const getScanTitle = () => {
    switch (scanType) {
      case 'front': return 'Front Face';
      case 'right': return 'Right Side';
      case 'left': return 'Left Side';
    }
  };

  const getInstructionText = () => {
    switch (scanType) {
      case 'front': return 'Center your face in the frame';
      case 'right': return 'Turn your head to show the right side';
      case 'left': return 'Turn your head to show the left side';
    }
  };

  if (!permission) {
    return (
      <View style={styles.container}>
        <Body>Requesting camera permission...</Body>
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={styles.permissionContainer}>
        <Stack.Screen options={{ 
          title: "Skin Scan",
          headerLeft: () => (
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <ArrowLeft size={24} color={colors.text.primary} />
            </TouchableOpacity>
          ),
        }} />
        <View style={styles.permissionContent}>
          <H3 style={styles.permissionTitle}>Camera Permission Required</H3>
          <Body style={styles.permissionText}>
            We need camera access to scan your face for skin analysis
          </Body>
          <Button title="Grant Permission" onPress={requestPermission} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ 
        title: getScanTitle(),
        headerLeft: () => (
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ArrowLeft size={24} color={colors.text.primary} />
          </TouchableOpacity>
        ),
      }} />
      
      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          facing={facing}
          ref={cameraRef}
        />
        <CameraFrame type={scanType} />
        <View style={styles.instructionContainer}>
          <View style={styles.instructionBox}>
            <Body style={styles.instructionText}>
              {getInstructionText()}
            </Body>
          </View>
        </View>
      </View>
      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.galleryButton}
          onPress={handlePickImage}
        >
          <ImageIcon size={24} color={colors.text.light} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.captureButton}
          onPress={handleTakePhoto}
        >
          <View style={styles.captureButtonInner} />
        </TouchableOpacity>
        <View style={styles.placeholder} />
      </View>

      <Modal visible={showModal} transparent animationType="fade">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <H3 style={styles.modalTitle}>Review Photo</H3>
            <Image
              source={{ uri: capturedImage || "" }}
              style={styles.previewImage}
              contentFit="cover"
            />
            <View style={styles.modalButtons}>
              <Button
                title="Retake"
                onPress={handleRetake}
                variant="outline"
                icon={<X size={20} color={colors.primary} />}
                style={styles.modalButton}
              />
              <Button
                title="Use Photo"
                onPress={handleUsePhoto}
                icon={<Check size={20} color={colors.text.light} />}
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  instructionContainer: {
    position: "absolute",
    top: 40,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  instructionBox: {
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    paddingVertical: spacing.s,
    paddingHorizontal: spacing.m,
    borderRadius: borderRadius.round,
  },
  instructionText: {
    color: colors.text.light,
    textAlign: "center",
  },
  controls: {
    position: "absolute",
    bottom: 40,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    alignItems: "center",
    justifyContent: "center",
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: colors.text.light,
  },
  captureButtonInner: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: colors.text.light,
  },
  placeholder: {
    width: 50,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.l,
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.l,
    padding: spacing.l,
    width: "100%",
    maxWidth: 400,
  },
  modalTitle: {
    marginBottom: spacing.m,
    textAlign: "center",
  },
  previewImage: {
    width: "100%",
    height: 400,
    borderRadius: borderRadius.m,
    marginBottom: spacing.l,
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  modalButton: {
    flex: 0.48,
  },
  permissionContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  permissionContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xl,
  },
  permissionTitle: {
    marginBottom: spacing.m,
  },
  permissionText: {
    textAlign: "center",
    marginBottom: spacing.xl,
    color: colors.text.secondary,
  },
  backButton: {
    padding: spacing.xs,
  },
});
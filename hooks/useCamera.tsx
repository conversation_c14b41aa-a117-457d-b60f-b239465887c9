import React, { useState, useRef, useEffect } from 'react';
import { CameraType, useCameraPermissions } from 'expo-camera';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

interface CameraHookReturn {
  permission: any;
  requestPermission: () => void;
  facing: CameraType;
  capturedImage: string | null;
  showModal: boolean;
  closeModal: () => void;
  cameraRef: React.RefObject<any>;
  isReady: boolean;
  handleTakePhoto: () => Promise<void>;
  handlePickImage: () => Promise<void>;
  flipCamera: () => void;
  onCameraReady: () => void;
}

export function useCamera(pageId: string): CameraHookReturn {
  const [permission, requestPermission] = useCameraPermissions();
  // TOUJOURS utiliser la caméra selfie par défaut pour toutes les pages
  const [facing, setFacing] = useState<CameraType>('front');
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const cameraRef = useRef<any>(null);

  console.log(`📸 [${pageId}] Camera state - isReady:`, isReady, 'permission:', permission?.granted);

  // Auto-request permission when component mounts
  useEffect(() => {
    if (permission === null) {
      console.log(`📸 [${pageId}] Permission loading...`);
      return;
    }
    
    if (!permission.granted && permission.canAskAgain) {
      console.log(`📸 [${pageId}] Auto-requesting permission`);
      requestPermission();
    }
  }, [permission, requestPermission, pageId]);

  // Set camera ready when permission is granted
  useEffect(() => {
    if (permission?.granted) {
      console.log(`📸 [${pageId}] Permission granted, waiting for camera to initialize`);
      // Add delay to ensure camera is fully initialized
      setTimeout(() => {
        setIsReady(true);
      }, 1000);
    } else {
      setIsReady(false);
    }
  }, [permission?.granted, pageId]);

  const flipCamera = () => {
    console.log(`📸 [${pageId}] Flipping camera from ${facing} to ${facing === 'front' ? 'back' : 'front'}`);
    const newFacing = facing === 'front' ? 'back' : 'front';
    setFacing(newFacing);
    setIsReady(false);
    
    // Wait for camera to stabilize after flip
    setTimeout(() => {
      console.log(`📸 [${pageId}] Camera ready after flip`);
      if (permission?.granted) {
        setIsReady(true);
      }
    }, 800);
  };

  const handleTakePhoto = async () => {
    console.log(`📸 [${pageId}] Taking photo - isReady:`, isReady, 'cameraRef:', !!cameraRef.current);
    
    if (!cameraRef.current) {
      console.log(`📸 [${pageId}] Camera ref not available`);
      return;
    }
    
    // Force ready state if permission is granted but isReady is false
    if (!isReady && permission?.granted) {
      console.log(`📸 [${pageId}] Force enabling camera`);
      setIsReady(true);
      // Wait a bit for state to update
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    try {
      console.log(`📸 [${pageId}] Capturing photo...`);
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });
      
      console.log(`📸 [${pageId}] Photo captured successfully:`, photo?.uri);
      
      if (photo?.uri) {
        setCapturedImage(photo.uri);
        setShowModal(true);
      } else {
        throw new Error('No photo URI returned');
      }
    } catch (error) {
      console.error(`📸 [${pageId}] Error taking picture:`, error);
      
      // For side scans, try flipping camera automatically
      if (pageId.includes('right') || pageId.includes('left')) {
        console.log(`📸 [${pageId}] Auto-retrying with camera flip for side scan`);
        flipCamera();
        
        // Try again after flip
        setTimeout(async () => {
          try {
            if (!cameraRef.current) {
              throw new Error('Camera ref lost after flip');
            }
            
            const photo = await cameraRef.current.takePictureAsync({
              quality: 0.8,
              base64: false,
              skipProcessing: false,
            });
            
            if (photo?.uri) {
              setCapturedImage(photo.uri);
              setShowModal(true);
            } else {
              throw new Error('No photo URI returned on retry');
            }
          } catch (retryError) {
            console.error(`📸 [${pageId}] Retry failed:`, retryError);
            Alert.alert(
              "Camera Error", 
              "Unable to take photo. Please try using the gallery instead.",
              [
                { text: "Cancel", style: "cancel" },
                { text: "Use Gallery", onPress: handlePickImage }
              ]
            );
          }
        }, 1000);
      } else {
        Alert.alert(
          "Camera Error", 
          "Unable to take photo. Please try again or use gallery.",
          [
            { text: "Try Again", onPress: handleTakePhoto },
            { text: "Use Gallery", onPress: handlePickImage }
          ]
        );
      }
    }
  };

  const handlePickImage = async () => {
    console.log(`📸 [${pageId}] Opening image picker`);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [3, 4],
        quality: 0.8,
      });

      console.log(`📸 [${pageId}] Image picker result:`, result.canceled ? 'cancelled' : 'success');

      if (!result.canceled && result.assets?.[0]?.uri) {
        setCapturedImage(result.assets[0].uri);
        setShowModal(true);
      }
    } catch (error) {
      console.error(`📸 [${pageId}] Error picking image:`, error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const closeModal = () => {
    console.log(`📸 [${pageId}] Closing modal`);
    setShowModal(false);
    setCapturedImage(null);
  };

  const onCameraReady = () => {
    console.log(`📸 [${pageId}] Camera ready callback triggered`);
    if (permission?.granted) {
      console.log(`📸 [${pageId}] Camera marked as ready`);
      // Add small delay to ensure camera is fully ready
      setTimeout(() => {
        setIsReady(true);
      }, 500);
    }
  };

  return {
    permission,
    requestPermission,
    facing,
    capturedImage,
    showModal,
    closeModal,
    cameraRef,
    isReady,
    handleTakePhoto,
    handlePickImage,
    flipCamera,
    onCameraReady,
  };
}
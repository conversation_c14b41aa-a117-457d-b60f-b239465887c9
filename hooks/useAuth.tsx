import createContextHook from '@nkzw/create-context-hook';
import { useEffect, useState } from 'react';
import { UserProfile } from '@/types';
import { router } from 'expo-router';
import { storage, STORAGE_KEYS } from '@/lib/storage';

// Mock user data
const mockUser: UserProfile = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  quizCompleted: false,
  scanCompleted: false,
  subscribed: false,
};

export const [AuthProvider, useAuth] = createContextHook(() => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Load user from storage on mount
  useEffect(() => {
    const loadUser = async () => {
      try {
        const storedUser = await storage.getItem<UserProfile>(STORAGE_KEYS.USER);
        if (storedUser) {
          setUser(storedUser);
        }
        setLoading(false);
      } catch (error) {
        console.error('Failed to load user:', error);
        setLoading(false);
      }
    };

    loadUser();
  }, []);

  // Save user to storage when it changes
  useEffect(() => {
    const saveUser = async () => {
      if (user) {
        try {
          await storage.setItem(STORAGE_KEYS.USER, user);
        } catch (error) {
          console.error('Failed to save user:', error);
        }
      }
    };

    if (user) {
      saveUser();
    }
  }, [user]);

  const signIn = async (email: string, password: string) => {
    // Mock authentication
    setLoading(true);
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUser(mockUser);
      return true;
    } catch (error) {
      console.error('Sign in failed:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const signInWithProvider = async (provider: 'google' | 'apple') => {
    // Mock provider authentication
    setLoading(true);
    try {
      // In a real app, this would use Expo Auth Session
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUser(mockUser);
      return true;
    } catch (error) {
      console.error(`Sign in with ${provider} failed:`, error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (name: string, email: string, password: string) => {
    // Mock sign up
    setLoading(true);
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUser({
        ...mockUser,
        name,
        email,
      });
      return true;
    } catch (error) {
      console.error('Sign up failed:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      await storage.removeItem(STORAGE_KEYS.USER);
      setUser(null);
      router.replace('/onboarding/welcome');
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  };

  const updateUser = (updates: Partial<UserProfile>) => {
    if (user) {
      setUser({ ...user, ...updates });
    }
  };

  return {
    user,
    loading,
    signIn,
    signInWithProvider,
    signUp,
    signOut,
    updateUser,
    isAuthenticated: !!user,
  };
});
import createContextHook from '@nkzw/create-context-hook';
import { useEffect, useState } from 'react';
import { 
  QuizAnswers, 
  RoutineStep, 
  ScanResult, 
  SkinScore, 
  DailyTip 
} from '@/types';
import { dailyTips, mockRoutineSteps } from '@/constants/mockData';
import { useAuth } from './useAuth';
import { storage, STORAGE_KEYS } from '@/lib/storage';
import { skincareService } from '@/services/skincare';

export const [SkincareProvider, useSkincare] = createContextHook(() => {
  const { user, updateUser } = useAuth();
  const [quizAnswers, setQuizAnswers] = useState<QuizAnswers>({});
  const [scanResults, setScanResults] = useState<ScanResult>({});
  const [skinScore, setSkinScore] = useState<SkinScore | null>(null);
  const [routineSteps, setRoutineSteps] = useState<RoutineStep[]>([]);
  const [dailyTip, setDailyTip] = useState<DailyTip | null>(null);
  const [loading, setLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [
          storedQuizAnswers,
          storedScanResults,
          storedSkinScore,
          storedRoutineSteps,
        ] = await Promise.all([
          storage.getItem<QuizAnswers>(STORAGE_KEYS.QUIZ_ANSWERS),
          storage.getItem<ScanResult>(STORAGE_KEYS.SCAN_RESULTS),
          storage.getItem<SkinScore>('skinScore'),
          storage.getItem<RoutineStep[]>('routineSteps'),
        ]);

        if (storedQuizAnswers) setQuizAnswers(storedQuizAnswers);
        if (storedScanResults) setScanResults(storedScanResults);
        if (storedSkinScore) setSkinScore(storedSkinScore);
        if (storedRoutineSteps) {
          setRoutineSteps(storedRoutineSteps);
        } else {
          // Use mock data if no stored data
          setRoutineSteps(mockRoutineSteps);
        }

        // Set a random daily tip
        const randomTip = dailyTips[Math.floor(Math.random() * dailyTips.length)];
        setDailyTip(randomTip);

        setLoading(false);
      } catch (error) {
        console.error('Failed to load skincare data:', error);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Save data to storage when it changes
  useEffect(() => {
    const saveData = async () => {
      try {
        await Promise.all([
          storage.setItem(STORAGE_KEYS.QUIZ_ANSWERS, quizAnswers),
          storage.setItem(STORAGE_KEYS.SCAN_RESULTS, scanResults),
          storage.setItem('routineSteps', routineSteps),
        ]);

        if (skinScore) {
          await storage.setItem('skinScore', skinScore);
        }
      } catch (error) {
        console.error('Failed to save skincare data:', error);
      }
    };

    saveData();
  }, [quizAnswers, scanResults, skinScore, routineSteps]);

  const saveQuizAnswer = (questionId: string, answerId: string) => {
    const updatedAnswers = { ...quizAnswers, [questionId]: answerId };
    setQuizAnswers(updatedAnswers);
  };

  const saveScanResult = (type: 'front' | 'right' | 'left', imageUri: string) => {
    console.log('🔬 [useSkincare] Saving scan result:', type, imageUri);
    const updatedResults = { ...scanResults, [type]: imageUri };
    console.log('🔬 [useSkincare] Updated scan results:', updatedResults);
    setScanResults(updatedResults);
  };

  const analyzeSkin = async () => {
    setLoading(true);
    try {
      const result = await skincareService.analyzeSkin(quizAnswers, scanResults);
      setSkinScore(result);
      
      // Update user profile
      if (user) {
        updateUser({ 
          skinScore: result,
          scanCompleted: true,
        });
      }
      
      return result;
    } catch (error) {
      console.error('Skin analysis failed:', error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const completeQuiz = () => {
    if (user) {
      updateUser({ quizCompleted: true });
    }
  };

  const toggleRoutineStep = (stepId: string, completed: boolean) => {
    const updatedSteps = routineSteps.map(step => 
      step.id === stepId ? { ...step, completed } : step
    );
    setRoutineSteps(updatedSteps);
  };

  const resetProgress = async () => {
    try {
      await Promise.all([
        storage.removeItem(STORAGE_KEYS.QUIZ_ANSWERS),
        storage.removeItem(STORAGE_KEYS.SCAN_RESULTS),
        storage.removeItem('skinScore'),
      ]);
      
      setQuizAnswers({});
      setScanResults({});
      setSkinScore(null);
      
      if (user) {
        updateUser({ 
          quizCompleted: false,
          scanCompleted: false,
          skinScore: undefined,
        });
      }
    } catch (error) {
      console.error('Failed to reset progress:', error);
    }
  };

  return {
    quizAnswers,
    scanResults,
    skinScore,
    routineSteps,
    dailyTip,
    loading,
    saveQuizAnswer,
    saveScanResult,
    analyzeSkin,
    completeQuiz,
    toggleRoutineStep,
    resetProgress,
    isQuizCompleted: user?.quizCompleted || false,
    isScanCompleted: user?.scanCompleted || false,
    isSubscribed: user?.subscribed || false,
  };
});